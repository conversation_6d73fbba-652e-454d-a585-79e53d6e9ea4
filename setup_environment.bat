@echo off
REM سكريبت Batch لإعداد بيئة Python للتطوير على الويب في Windows

echo === بدء إعداد بيئة Python للتطوير على الويب ===

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo تم العثور على Python
    set PYTHON_CMD=python
) else (
    python3 --version >nul 2>&1
    if %errorlevel% == 0 (
        echo تم العثور على Python3
        set PYTHON_CMD=python3
    ) else (
        echo خطأ: Python غير مثبت. يرجى تثبيت Python من https://python.org
        pause
        exit /b 1
    )
)

REM إنشاء البيئة الافتراضية
echo إنشاء البيئة الافتراضية...
%PYTHON_CMD% -m venv web_env

if %errorlevel% neq 0 (
    echo خطأ في إنشاء البيئة الافتراضية
    pause
    exit /b 1
)

REM تفعيل البيئة الافتراضية
echo تفعيل البيئة الافتراضية...
call web_env\Scripts\activate.bat

REM ترقية pip
echo ترقية pip...
pip install --upgrade pip

REM تثبيت أدوات البناء الأساسية
echo تثبيت أدوات البناء الأساسية...
pip install wheel setuptools

REM تثبيت المكتبات من requirements.txt
echo تثبيت المكتبات من requirements.txt...
pip install -r requirements.txt

REM تثبيت مكتبات إضافية مهمة
echo تثبيت مكتبات إضافية...
pip install ipython jupyter notebook

echo === تم إكمال إعداد البيئة بنجاح ===
echo.
echo لتفعيل البيئة الافتراضية في المستقبل:
echo web_env\Scripts\activate.bat
echo.
echo لإلغاء تفعيل البيئة:
echo deactivate
echo.
echo لبدء Jupyter Notebook:
echo jupyter notebook
echo.
pause
