#!/bin/bash
# دليل تثبيت Python على أنظمة التشغيل المختلفة

echo "=== دليل تثبيت Python ==="

# Ubuntu/Debian
echo "للتثبيت على Ubuntu/Debian:"
echo "sudo apt update"
echo "sudo apt install python3 python3-pip python3-venv python3-dev"
echo ""

# CentOS/RHEL
echo "للتثبيت على CentOS/RHEL:"
echo "sudo yum install python3 python3-pip python3-venv python3-devel"
echo ""

# Fedora
echo "للتثبيت على Fedora:"
echo "sudo dnf install python3 python3-pip python3-venv python3-devel"
echo ""

# macOS
echo "للتثبيت على macOS:"
echo "# باستخدام Homebrew:"
echo "brew install python3"
echo ""
echo "# أو تحميل من الموقع الرسمي:"
echo "# https://www.python.org/downloads/macos/"
echo ""

# Windows
echo "للتثبيت على Windows:"
echo "1. اذهب إلى: https://www.python.org/downloads/windows/"
echo "2. حمل أحدث إصدار من Python 3"
echo "3. شغل ملف التثبيت"
echo "4. تأكد من تحديد 'Add Python to PATH'"
echo "5. اختر 'Install Now'"
echo ""

echo "بعد التثبيت، تحقق من النجاح بتشغيل:"
echo "python --version"
echo "أو"
echo "python3 --version"