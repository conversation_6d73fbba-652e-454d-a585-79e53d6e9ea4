# دليل إعداد Python للتطوير على الويب

مرحباً بك في دليل إعداد بيئة Python الشاملة للتطوير على الويب. هذا المشروع يحتوي على كل ما تحتاجه لبدء تطوير تطبيقات الويب باستخدام Python.

## 📋 المحتويات

- [متطلبات النظام](#متطلبات-النظام)
- [التثبيت السريع](#التثبيت-السريع)
- [الملفات المتضمنة](#الملفات-المتضمنة)
- [إطارات العمل المدعومة](#إطارات-العمل-المدعومة)
- [قواعد البيانات](#قواعد-البيانات)
- [أمثلة التطبيقات](#أمثلة-التطبيقات)
- [الاستخدام](#الاستخدام)

## 🔧 متطلبات النظام

- **Python 3.8+** (يُفضل Python 3.9 أو أحدث)
- **pip** (مدير حزم Python)
- **Git** (اختياري)
- **قاعدة بيانات** (MySQL, PostgreSQL, SQLite, MongoDB, Redis)

## ⚡ التثبيت السريع

### على Windows:

```batch
# تشغيل ملف الإعداد
setup_environment.bat
```

أو باستخدام PowerShell:

```powershell
# تشغيل ملف PowerShell
.\setup_environment.ps1
```

### على Linux/Mac:

```bash
# إعطاء صلاحيات التنفيذ
chmod +x setup_environment.sh

# تشغيل ملف الإعداد
./setup_environment.sh
```

## 📁 الملفات المتضمنة

### ملفات الإعداد:
- `requirements.txt` - قائمة شاملة بجميع المكتبات المطلوبة
- `setup_environment.sh` - سكريبت إعداد للـ Linux/Mac
- `setup_environment.ps1` - سكريبت PowerShell للـ Windows
- `setup_environment.bat` - سكريبت Batch للـ Windows

### ملفات التكوين:
- `database_config.py` - إعدادات قواعد البيانات المختلفة
- `django_setup.py` - معالج إعداد مشاريع Django
- `django_settings.py` - إعدادات Django محسنة

### أمثلة التطبيقات:
- `flask_app_example.py` - تطبيق Flask شامل
- `wow.py` - اختبار المكتبات المثبتة
- `templates/` - قوالب HTML للتطبيقات

## 🚀 إطارات العمل المدعومة

### إطارات الويب الرئيسية:
- **Django** - إطار عمل شامل للتطبيقات الكبيرة
- **Flask** - إطار عمل مرن وبسيط
- **FastAPI** - إطار عمل حديث وسريع للـ APIs
- **Tornado** - إطار عمل غير متزامن

### خوادم التطبيقات:
- **Gunicorn** - خادم WSGI للإنتاج
- **Uvicorn** - خادم ASGI للتطبيقات غير المتزامنة
- **Waitress** - خادم WSGI متعدد المنصات

## 🗄️ قواعد البيانات

### قواعد البيانات العلائقية:
- **SQLite** - قاعدة بيانات محلية بسيطة
- **MySQL** - قاعدة بيانات شائعة ومستقرة
- **PostgreSQL** - قاعدة بيانات متقدمة ومفتوحة المصدر
- **Oracle** - قاعدة بيانات المؤسسات

### قواعد البيانات غير العلائقية:
- **MongoDB** - قاعدة بيانات المستندات
- **Redis** - قاعدة بيانات في الذاكرة للتخزين المؤقت

## 📚 المكتبات المتضمنة

### مكتبات الويب الأساسية:
- `requests` - للطلبات HTTP
- `httpx` - طلبات HTTP غير متزامنة
- `aiohttp` - خادم وعميل HTTP غير متزامن
- `beautifulsoup4` - لتحليل HTML
- `scrapy` - لاستخراج البيانات من الويب

### مكتبات قواعد البيانات:
- `SQLAlchemy` - ORM قوي ومرن
- `pymongo` - تعامل مع MongoDB
- `redis` - تعامل مع Redis
- `psycopg2-binary` - تعامل مع PostgreSQL
- `mysql-connector-python` - تعامل مع MySQL

### مكتبات الأمان:
- `cryptography` - تشفير متقدم
- `bcrypt` - تشفير كلمات المرور
- `PyJWT` - التعامل مع JWT tokens
- `passlib` - إدارة كلمات المرور

### مكتبات التطوير:
- `pytest` - إطار اختبار شامل
- `black` - تنسيق الكود
- `flake8` - فحص جودة الكود
- `mypy` - فحص الأنواع

## 🎯 أمثلة التطبيقات

### تطبيق Flask:
```python
# تشغيل تطبيق Flask المثال
python flask_app_example.py
```

يتضمن التطبيق:
- نظام تسجيل المستخدمين
- إدارة المنشورات
- واجهة برمجة التطبيقات (API)
- قوالب HTML جاهزة

### إعداد Django:
```python
# إنشاء مشروع Django جديد
python django_setup.py
```

## 🔧 الاستخدام

### 1. تفعيل البيئة الافتراضية:

**Windows:**
```batch
web_env\Scripts\activate
```

**Linux/Mac:**
```bash
source web_env/bin/activate
```

### 2. اختبار التثبيت:
```python
python wow.py
```

### 3. تشغيل تطبيق Flask:
```python
python flask_app_example.py
```

### 4. إنشاء مشروع Django:
```python
python django_setup.py
```

## 🌐 الوصول للتطبيقات

- **Flask App**: http://localhost:5000
- **Django App**: http://localhost:8000
- **Jupyter Notebook**: http://localhost:8888

## 📝 ملاحظات مهمة

1. **البيئة الافتراضية**: تأكد من تفعيل البيئة الافتراضية قبل العمل
2. **قواعد البيانات**: قم بإعداد قاعدة البيانات المطلوبة قبل تشغيل التطبيقات
3. **الأمان**: غيّر المفاتيح السرية في بيئة الإنتاج
4. **التحديثات**: قم بتحديث المكتبات بانتظام

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تثبيت المكتبات:**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

2. **مشاكل في قاعدة البيانات:**
   - تأكد من تشغيل خادم قاعدة البيانات
   - تحقق من بيانات الاتصال

3. **مشاكل في الاستيراد:**
   - تأكد من تفعيل البيئة الافتراضية
   - أعد تثبيت المكتبة المطلوبة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `django.log` للأخطاء
2. تأكد من تثبيت جميع المتطلبات
3. راجع وثائق إطار العمل المستخدم

---

**ملاحظة:** هذا المشروع يحتوي على بيئة تطوير كاملة. في بيئة الإنتاج، تأكد من تطبيق إجراءات الأمان المناسبة.
