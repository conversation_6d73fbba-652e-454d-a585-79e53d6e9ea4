#!/bin/bash
# سكريبت إعداد بيئة Python للتطوير على الويب

echo "=== بدء إعداد بيئة Python للتطوير على الويب ==="

# التحقق من وجود Python
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    echo "خطأ: Python غير مثبت. يرجى تثبيت Python أولاً."
    exit 1
fi

# تحديد أمر Python المناسب
if command -v python3 &> /dev/null; then
    PYTHON_CMD=python3
else
    PYTHON_CMD=python
fi

echo "استخدام $PYTHON_CMD"

# إنشاء بيئة افتراضية
echo "إنشاء البيئة الافتراضية..."
$PYTHON_CMD -m venv web_env

# تفعيل البيئة الافتراضية
echo "تفعيل البيئة الافتراضية..."
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    # Windows
    source web_env/Scripts/activate
else
    # Linux/Mac
    source web_env/bin/activate
fi

# ترقية pip
echo "ترقية pip..."
pip install --upgrade pip

# تثبيت wheel و setuptools
echo "تثبيت أدوات البناء الأساسية..."
pip install wheel setuptools

# تثبيت المكتبات من requirements.txt
echo "تثبيت المكتبات من requirements.txt..."
pip install -r requirements.txt

# تثبيت مكتبات إضافية مهمة
echo "تثبيت مكتبات إضافية..."
pip install ipython jupyter notebook

echo "=== تم إكمال إعداد البيئة بنجاح ==="
echo ""
echo "لتفعيل البيئة الافتراضية:"
echo "على Windows: web_env\\Scripts\\activate"
echo "على Linux/Mac: source web_env/bin/activate"
echo ""
echo "لإلغاء تفعيل البيئة: deactivate"