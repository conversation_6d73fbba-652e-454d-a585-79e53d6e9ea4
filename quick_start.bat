@echo off
REM سكريبت البدء السريع لبيئة Python للويب

echo ========================================
echo    مرحباً بك في بيئة Python للويب
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت!
    echo.
    echo 📖 يرجى قراءة ملف install_python_windows.md للحصول على تعليمات التثبيت
    echo 🌐 أو اذهب إلى: https://www.python.org/downloads/windows/
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version
echo.

REM عرض الخيارات
echo اختر ما تريد فعله:
echo.
echo 1. إعداد البيئة الافتراضية وتثبيت المكتبات
echo 2. اختبار المكتبات المثبتة
echo 3. تشغيل تطبيق Flask المثال
echo 4. تشغيل تطبيق FastAPI المثال
echo 5. إنشاء مشروع Django جديد
echo 6. فتح Jupyter Notebook
echo 7. عرض معلومات المشروع
echo 0. خروج
echo.

set /p choice="أدخل اختيارك (0-7): "

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto test
if "%choice%"=="3" goto flask
if "%choice%"=="4" goto fastapi
if "%choice%"=="5" goto django
if "%choice%"=="6" goto jupyter
if "%choice%"=="7" goto info
if "%choice%"=="0" goto exit
goto invalid

:setup
echo.
echo 🔧 إعداد البيئة...
call setup_environment.bat
goto end

:test
echo.
echo 🧪 اختبار المكتبات...
python test_installation.py
goto end

:flask
echo.
echo 🌶️ تشغيل تطبيق Flask...
echo 🌐 سيكون متاحاً على: http://localhost:5000
echo 📖 الوثائق: اقرأ flask_app_example.py
echo.
python flask_app_example.py
goto end

:fastapi
echo.
echo ⚡ تشغيل تطبيق FastAPI...
echo 🌐 سيكون متاحاً على: http://localhost:8000
echo 📖 الوثائق: http://localhost:8000/docs
echo.
python fastapi_example.py
goto end

:django
echo.
echo 🎯 إنشاء مشروع Django...
python django_setup.py
goto end

:jupyter
echo.
echo 📓 تشغيل Jupyter Notebook...
echo 🌐 سيفتح في المتصفح تلقائياً
echo.
jupyter notebook
goto end

:info
echo.
echo 📋 معلومات المشروع:
echo ==================
echo.
echo 📁 الملفات المتاحة:
echo   - requirements.txt: قائمة المكتبات
echo   - flask_app_example.py: مثال Flask
echo   - fastapi_example.py: مثال FastAPI
echo   - database_config.py: إعدادات قواعد البيانات
echo   - test_installation.py: اختبار المكتبات
echo.
echo 📚 إطارات العمل المدعومة:
echo   - Django: إطار عمل شامل
echo   - Flask: إطار عمل مرن
echo   - FastAPI: إطار عمل حديث للـ APIs
echo   - Tornado: إطار عمل غير متزامن
echo.
echo 🗄️ قواعد البيانات المدعومة:
echo   - SQLite: قاعدة بيانات محلية
echo   - MySQL: قاعدة بيانات شائعة
echo   - PostgreSQL: قاعدة بيانات متقدمة
echo   - MongoDB: قاعدة بيانات NoSQL
echo   - Redis: تخزين مؤقت
echo.
echo 📖 للمزيد من المعلومات، اقرأ README.md
goto end

:invalid
echo.
echo ❌ اختيار غير صحيح. يرجى اختيار رقم من 0 إلى 7
goto end

:exit
echo.
echo 👋 شكراً لاستخدام بيئة Python للويب!
exit /b 0

:end
echo.
echo 🔄 هل تريد العودة للقائمة الرئيسية؟ (y/n)
set /p return="اختيارك: "
if /i "%return%"=="y" goto start
if /i "%return%"=="yes" goto start
if /i "%return%"=="نعم" goto start

echo.
echo 👋 إلى اللقاء!
pause
exit /b 0

:start
cls
goto begin

:begin
goto :eof
