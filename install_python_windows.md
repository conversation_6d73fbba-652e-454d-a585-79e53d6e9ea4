# دليل تثبيت Python على Windows

## 📥 تحميل Python

### الطريقة الأولى: من الموقع الرسمي (مُوصى بها)

1. **اذهب إلى الموقع الرسمي:**
   - [https://www.python.org/downloads/windows/](https://www.python.org/downloads/windows/)

2. **حمل أحدث إصدار:**
   - اختر "Latest Python 3.x Release"
   - حمل "Windows installer (64-bit)" للأنظمة 64-bit
   - أو "Windows installer (32-bit)" للأنظمة 32-bit

### الطريقة الثانية: من Microsoft Store

1. افتح Microsoft Store
2. ابحث عن "Python"
3. اختر "Python 3.x" من Microsoft Corporation
4. اضغط "Get" أو "Install"

## 🔧 تثبيت Python

### خطوات التثبيت:

1. **شغل ملف التثبيت:**
   - انقر نقراً مزدوجاً على الملف المحمل

2. **إعدادات مهمة:**
   - ✅ **تأكد من تحديد "Add Python to PATH"**
   - ✅ تحديد "Install launcher for all users"

3. **اختر نوع التثبيت:**
   - **Install Now** (مُوصى للمبتدئين)
   - أو **Customize installation** للخيارات المتقدمة

4. **في حالة Customize installation:**
   - تأكد من تحديد:
     - pip
     - tcl/tk and IDLE
     - Python test suite
     - py launcher
     - for all users

5. **Advanced Options:**
   - ✅ Install for all users
   - ✅ Add Python to environment variables
   - ✅ Precompile standard library
   - اختر مجلد التثبيت (اختياري)

## ✅ التحقق من التثبيت

### افتح Command Prompt أو PowerShell وشغل:

```cmd
python --version
```

أو:

```cmd
python3 --version
```

يجب أن ترى شيئاً مثل:
```
Python 3.11.5
```

### تحقق من pip:

```cmd
pip --version
```

يجب أن ترى شيئاً مثل:
```
pip 23.2.1 from C:\Python311\lib\site-packages\pip (python 3.11)
```

## 🛠️ إعداد البيئة

### إنشاء بيئة افتراضية:

```cmd
python -m venv myenv
```

### تفعيل البيئة الافتراضية:

**Command Prompt:**
```cmd
myenv\Scripts\activate
```

**PowerShell:**
```powershell
myenv\Scripts\Activate.ps1
```

### إلغاء تفعيل البيئة:

```cmd
deactivate
```

## 🔧 حل المشاكل الشائعة

### المشكلة: "Python was not found"

**الحل:**
1. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
2. أو أضف Python يدوياً إلى PATH:
   - اذهب إلى Control Panel > System > Advanced System Settings
   - اضغط "Environment Variables"
   - في System Variables، ابحث عن "Path"
   - أضف مجلد Python (مثل: C:\Python311\)
   - أضف مجلد Scripts (مثل: C:\Python311\Scripts\)

### المشكلة: "pip is not recognized"

**الحل:**
```cmd
python -m pip --version
```

أو أعد تثبيت pip:
```cmd
python -m ensurepip --upgrade
```

### المشكلة: مشاكل في الصلاحيات

**الحل:**
- شغل Command Prompt كـ Administrator
- أو استخدم `--user` مع pip:
```cmd
pip install --user package_name
```

## 📦 تثبيت المكتبات الأساسية

بعد تثبيت Python، يمكنك تثبيت المكتبات:

```cmd
# ترقية pip
python -m pip install --upgrade pip

# تثبيت المكتبات الأساسية
pip install requests flask django fastapi

# أو تثبيت من ملف requirements.txt
pip install -r requirements.txt
```

## 🚀 الخطوات التالية

1. **تشغيل سكريبت الإعداد:**
   ```cmd
   setup_environment.bat
   ```

2. **أو باستخدام PowerShell:**
   ```powershell
   .\setup_environment.ps1
   ```

3. **اختبار التثبيت:**
   ```cmd
   python wow.py
   ```

## 📚 موارد إضافية

- [وثائق Python الرسمية](https://docs.python.org/3/)
- [دليل pip](https://pip.pypa.io/en/stable/)
- [Virtual Environments](https://docs.python.org/3/tutorial/venv.html)

---

**ملاحظة:** إذا كنت تستخدم XAMPP، تأكد من عدم تعارض Python مع إعدادات Apache أو MySQL.
