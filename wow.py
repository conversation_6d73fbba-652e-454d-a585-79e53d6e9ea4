"""
مثال بسيط لتطبيق Python للويب
"""

print("مرحباً بك في عالم Python للويب!")
print("تم إعداد البيئة بنجاح")

# مثال على استخدام المكتبات
try:
    import requests
    print("✓ مكتبة requests متاحة")
except ImportError:
    print("✗ مكتبة requests غير متاحة")

try:
    import flask
    print("✓ مكتبة Flask متاحة")
except ImportError:
    print("✗ مكتبة Flask غير متاحة")

try:
    import django
    print("✓ مكتبة Django متاحة")
except ImportError:
    print("✗ مكتبة Django غير متاحة")

try:
    import fastapi
    print("✓ مكتبة FastAPI متاحة")
except ImportError:
    print("✗ مكتبة FastAPI غير متاحة")