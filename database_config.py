"""
ملف تكوين قواعد البيانات
يحتوي على أمثلة للاتصال بقواعد البيانات المختلفة
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    # SQLite (قاعدة بيانات محلية بسيطة)
    'sqlite': {
        'url': 'sqlite:///./app.db',
        'echo': True
    },
    
    # MySQL
    'mysql': {
        'host': 'localhost',
        'port': 3306,
        'username': 'root',
        'password': '',
        'database': 'webproject',
        'url': 'mysql+mysqlconnector://root:@localhost:3306/webproject'
    },
    
    # PostgreSQL
    'postgresql': {
        'host': 'localhost',
        'port': 5432,
        'username': 'postgres',
        'password': 'password',
        'database': 'webproject',
        'url': 'postgresql://postgres:password@localhost:5432/webproject'
    },
    
    # MongoDB (NoSQL)
    'mongodb': {
        'host': 'localhost',
        'port': 27017,
        'database': 'webproject',
        'url': 'mongodb://localhost:27017/webproject'
    },
    
    # Redis (للتخزين المؤقت)
    'redis': {
        'host': 'localhost',
        'port': 6379,
        'db': 0,
        'url': 'redis://localhost:6379/0'
    }
}

# إعداد SQLAlchemy
def create_database_engine(db_type='sqlite'):
    """إنشاء محرك قاعدة البيانات"""
    config = DATABASE_CONFIG.get(db_type)
    if not config:
        raise ValueError(f"نوع قاعدة البيانات غير مدعوم: {db_type}")
    
    engine = create_engine(
        config['url'],
        echo=config.get('echo', False)
    )
    return engine

# إنشاء جلسة قاعدة البيانات
def create_database_session(db_type='sqlite'):
    """إنشاء جلسة قاعدة البيانات"""
    engine = create_database_engine(db_type)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()

# Base class للنماذج
Base = declarative_base()

# مثال على نموذج بيانات
from sqlalchemy import Column, Integer, String, DateTime, Text
from datetime import datetime

class User(Base):
    """نموذج المستخدم"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    password_hash = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Post(Base):
    """نموذج المنشور"""
    __tablename__ = "posts"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200))
    content = Column(Text)
    author_id = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# دالة لإنشاء الجداول
def create_tables(db_type='sqlite'):
    """إنشاء جداول قاعدة البيانات"""
    engine = create_database_engine(db_type)
    Base.metadata.create_all(bind=engine)
    print(f"تم إنشاء الجداول في قاعدة البيانات: {db_type}")

# مثال على الاتصال بـ MongoDB
def get_mongodb_connection():
    """الاتصال بـ MongoDB"""
    try:
        from pymongo import MongoClient
        client = MongoClient(DATABASE_CONFIG['mongodb']['url'])
        db = client[DATABASE_CONFIG['mongodb']['database']]
        return db
    except ImportError:
        print("يرجى تثبيت pymongo: pip install pymongo")
        return None

# مثال على الاتصال بـ Redis
def get_redis_connection():
    """الاتصال بـ Redis"""
    try:
        import redis
        r = redis.Redis(
            host=DATABASE_CONFIG['redis']['host'],
            port=DATABASE_CONFIG['redis']['port'],
            db=DATABASE_CONFIG['redis']['db']
        )
        return r
    except ImportError:
        print("يرجى تثبيت redis: pip install redis")
        return None

if __name__ == "__main__":
    # اختبار الاتصال بقاعدة البيانات
    print("اختبار الاتصال بقواعد البيانات...")
    
    # إنشاء جداول SQLite
    create_tables('sqlite')
    
    # اختبار MongoDB
    mongo_db = get_mongodb_connection()
    if mongo_db:
        print("تم الاتصال بـ MongoDB بنجاح")
    
    # اختبار Redis
    redis_conn = get_redis_connection()
    if redis_conn:
        try:
            redis_conn.ping()
            print("تم الاتصال بـ Redis بنجاح")
        except:
            print("فشل الاتصال بـ Redis")
