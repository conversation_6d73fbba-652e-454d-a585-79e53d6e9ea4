# سكريبت PowerShell لإعداد بيئة Python للتطوير على الويب في Windows

Write-Host "=== بدء إعداد بيئة Python للتطوير على الويب ===" -ForegroundColor Green

# التحقق من وجود Python
try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "تم العثور على Python: $pythonVersion" -ForegroundColor Green
        $pythonCmd = "python"
    } else {
        $python3Version = python3 --version 2>$null
        if ($python3Version) {
            Write-Host "تم العثور على Python3: $python3Version" -ForegroundColor Green
            $pythonCmd = "python3"
        } else {
            throw "Python غير مثبت"
        }
    }
} catch {
    Write-Host "خطأ: Python غير مثبت. يرجى تثبيت Python من https://python.org" -ForegroundColor Red
    exit 1
}

# إنشاء البيئة الافتراضية
Write-Host "إنشاء البيئة الافتراضية..." -ForegroundColor Yellow
& $pythonCmd -m venv web_env

if ($LASTEXITCODE -ne 0) {
    Write-Host "خطأ في إنشاء البيئة الافتراضية" -ForegroundColor Red
    exit 1
}

# تفعيل البيئة الافتراضية
Write-Host "تفعيل البيئة الافتراضية..." -ForegroundColor Yellow
& .\web_env\Scripts\Activate.ps1

# ترقية pip
Write-Host "ترقية pip..." -ForegroundColor Yellow
pip install --upgrade pip

# تثبيت أدوات البناء الأساسية
Write-Host "تثبيت أدوات البناء الأساسية..." -ForegroundColor Yellow
pip install wheel setuptools

# تثبيت المكتبات من requirements.txt
Write-Host "تثبيت المكتبات من requirements.txt..." -ForegroundColor Yellow
pip install -r requirements.txt

# تثبيت مكتبات إضافية مهمة
Write-Host "تثبيت مكتبات إضافية..." -ForegroundColor Yellow
pip install ipython jupyter notebook

Write-Host "=== تم إكمال إعداد البيئة بنجاح ===" -ForegroundColor Green
Write-Host ""
Write-Host "لتفعيل البيئة الافتراضية في المستقبل:" -ForegroundColor Cyan
Write-Host ".\web_env\Scripts\Activate.ps1" -ForegroundColor White
Write-Host ""
Write-Host "لإلغاء تفعيل البيئة:" -ForegroundColor Cyan
Write-Host "deactivate" -ForegroundColor White
Write-Host ""
Write-Host "لبدء Jupyter Notebook:" -ForegroundColor Cyan
Write-Host "jupyter notebook" -ForegroundColor White
