{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - تطبيق الويب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <h1 class="mb-4">أحدث المنشورات</h1>
        
        {% if posts %}
            {% for post in posts %}
            <div class="card mb-3">
                <div class="card-body">
                    <h5 class="card-title">
                        <a href="{{ url_for('view_post', post_id=post.id) }}" class="text-decoration-none">
                            {{ post.title }}
                        </a>
                    </h5>
                    <p class="card-text">{{ post.content[:200] }}{% if post.content|length > 200 %}...{% endif %}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            بواسطة: {{ post.author.username }} | 
                            {{ post.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                        <a href="{{ url_for('view_post', post_id=post.id) }}" class="btn btn-primary btn-sm">
                            قراءة المزيد
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="alert alert-info">
                <h4>لا توجد منشورات حالياً</h4>
                <p>كن أول من ينشر محتوى على الموقع!</p>
                {% if not session.user_id %}
                <a href="{{ url_for('register') }}" class="btn btn-primary">سجل الآن</a>
                {% endif %}
            </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>مرحباً بك في تطبيق الويب</h5>
            </div>
            <div class="card-body">
                {% if session.user_id %}
                    <p>مرحباً {{ session.username }}!</p>
                    <a href="{{ url_for('create_post') }}" class="btn btn-success btn-block mb-2">
                        إنشاء منشور جديد
                    </a>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-info btn-block">
                        لوحة التحكم
                    </a>
                {% else %}
                    <p>انضم إلى مجتمعنا وابدأ في مشاركة المحتوى</p>
                    <a href="{{ url_for('register') }}" class="btn btn-primary btn-block mb-2">
                        إنشاء حساب جديد
                    </a>
                    <a href="{{ url_for('login') }}" class="btn btn-outline-primary btn-block">
                        تسجيل الدخول
                    </a>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>إحصائيات الموقع</h5>
            </div>
            <div class="card-body">
                <p><strong>عدد المنشورات:</strong> {{ posts|length }}</p>
                <p><strong>آخر تحديث:</strong> {{ moment().format('YYYY-MM-DD') }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // يمكن إضافة JavaScript هنا
    console.log('تم تحميل الصفحة الرئيسية');
</script>
{% endblock %}
