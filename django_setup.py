"""
سكريبت إعداد مشروع Django
يقوم بإنشاء مشروع Django جديد مع الإعدادات الأساسية
"""

import os
import subprocess
import sys

def run_command(command, description):
    """تشغيل أمر في سطر الأوامر"""
    print(f"تنفيذ: {description}")
    print(f"الأمر: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ تم بنجاح: {description}")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ فشل: {description}")
        print(f"خطأ: {e.stderr}")
        return False

def create_django_project():
    """إنشاء مشروع Django جديد"""
    project_name = "django_webproject"
    
    print("=== إعداد مشروع Django ===")
    
    # إنشاء مشروع Django
    if not run_command(f"django-admin startproject {project_name}", "إنشاء مشروع Django"):
        return False
    
    # الانتقال إلى مجلد المشروع
    os.chdir(project_name)
    
    # إنشاء تطبيق
    if not run_command("python manage.py startapp blog", "إنشاء تطبيق blog"):
        return False
    
    # إجراء الهجرات الأولية
    if not run_command("python manage.py makemigrations", "إنشاء ملفات الهجرة"):
        return False
    
    if not run_command("python manage.py migrate", "تطبيق الهجرات"):
        return False
    
    print("\n=== تم إنشاء مشروع Django بنجاح ===")
    print(f"مجلد المشروع: {project_name}")
    print("لتشغيل الخادم:")
    print(f"cd {project_name}")
    print("python manage.py runserver")
    
    return True

def create_django_settings():
    """إنشاء ملف إعدادات Django محسن"""
    settings_content = '''"""
إعدادات Django للمشروع
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-change-this-in-production'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',  # Django REST Framework
    'corsheaders',     # CORS headers
    'blog',            # تطبيق المدونة
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'django_webproject.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'django_webproject.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    },
    # إعدادات MySQL (اختيارية)
    'mysql': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'django_webproject',
        'USER': 'root',
        'PASSWORD': '',
        'HOST': 'localhost',
        'PORT': '3306',
    },
    # إعدادات PostgreSQL (اختيارية)
    'postgresql': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'django_webproject',
        'USER': 'postgres',
        'PASSWORD': 'password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'ar'
TIME_ZONE = 'Asia/Riyadh'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATICFILES_DIRS = [BASE_DIR / 'static']
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticatedOrReadOnly',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20
}

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# Email settings (للتطوير)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
'''
    
    with open('django_settings.py', 'w', encoding='utf-8') as f:
        f.write(settings_content)
    
    print("تم إنشاء ملف إعدادات Django")

if __name__ == "__main__":
    print("مرحباً بك في معالج إعداد Django")
    
    # التحقق من تثبيت Django
    try:
        import django
        print(f"تم العثور على Django الإصدار: {django.get_version()}")
    except ImportError:
        print("Django غير مثبت. يرجى تثبيته أولاً:")
        print("pip install Django")
        sys.exit(1)
    
    # إنشاء ملف الإعدادات
    create_django_settings()
    
    # سؤال المستخدم عن إنشاء مشروع جديد
    create_project = input("هل تريد إنشاء مشروع Django جديد؟ (y/n): ").lower()
    
    if create_project in ['y', 'yes', 'نعم']:
        create_django_project()
    else:
        print("يمكنك إنشاء مشروع Django يدوياً باستخدام:")
        print("django-admin startproject myproject")
