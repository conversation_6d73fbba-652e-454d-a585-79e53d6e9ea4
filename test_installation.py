#!/usr/bin/env python3
"""
سكريبت اختبار شامل للتحقق من تثبيت جميع المكتبات
"""

import sys
import importlib
import subprocess
from datetime import datetime

# قائمة المكتبات المطلوب اختبارها
REQUIRED_PACKAGES = [
    # إطارات العمل الأساسية
    ('django', 'Django'),
    ('flask', 'Flask'),
    ('fastapi', 'FastAPI'),
    ('tornado', 'Tornado'),
    
    # خوادم التطبيقات
    ('gunicorn', 'Gunicorn'),
    ('uvicorn', 'Uvicorn'),
    ('waitress', 'Waitress'),
    
    # قواعد البيانات
    ('sqlalchemy', 'SQLAlchemy'),
    ('pymongo', 'PyMongo'),
    ('redis', 'Redis'),
    ('mysql.connector', 'MySQL Connector'),
    
    # مكتبات HTTP
    ('requests', 'Requests'),
    ('httpx', 'HTTPX'),
    ('aiohttp', 'aiohttp'),
    
    # معالجة البيانات
    ('pandas', 'Pandas'),
    ('numpy', 'NumPy'),
    ('openpyxl', 'OpenPyXL'),
    
    # الأمان والتشفير
    ('cryptography', 'Cryptography'),
    ('bcrypt', 'bcrypt'),
    ('jwt', 'PyJWT'),
    ('passlib', 'Passlib'),
    
    # أدوات التطوير
    ('pytest', 'pytest'),
    ('black', 'Black'),
    ('flake8', 'Flake8'),
    
    # مكتبات إضافية
    ('jinja2', 'Jinja2'),
    ('pydantic', 'Pydantic'),
    ('python_dateutil', 'python-dateutil'),
    ('pytz', 'pytz'),
    ('pillow', 'Pillow'),
    ('beautifulsoup4', 'Beautiful Soup'),
    ('scrapy', 'Scrapy'),
    ('selenium', 'Selenium'),
    ('matplotlib', 'Matplotlib'),
    ('seaborn', 'Seaborn'),
    ('plotly', 'Plotly'),
    ('scipy', 'SciPy'),
    ('scikit-learn', 'scikit-learn'),
    ('boto3', 'Boto3'),
    ('click', 'Click'),
    ('colorama', 'Colorama'),
    ('tqdm', 'tqdm'),
    ('schedule', 'Schedule'),
    ('ipython', 'IPython'),
    ('jupyter', 'Jupyter'),
]

def check_python_version():
    """التحقق من إصدار Python"""
    print("=== معلومات Python ===")
    print(f"إصدار Python: {sys.version}")
    print(f"مسار Python: {sys.executable}")
    print(f"المنصة: {sys.platform}")
    
    version_info = sys.version_info
    if version_info.major < 3 or (version_info.major == 3 and version_info.minor < 8):
        print("⚠️  تحذير: يُنصح باستخدام Python 3.8 أو أحدث")
    else:
        print("✅ إصدار Python مناسب")
    print()

def check_pip():
    """التحقق من pip"""
    print("=== معلومات pip ===")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ pip متاح: {result.stdout.strip()}")
    except subprocess.CalledProcessError:
        print("❌ pip غير متاح")
        return False
    print()
    return True

def test_package_import(package_name, display_name):
    """اختبار استيراد مكتبة"""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, '__version__', 'غير محدد')
        return True, version
    except ImportError as e:
        return False, str(e)

def run_installation_test():
    """تشغيل اختبار التثبيت الشامل"""
    print("🔍 بدء اختبار التثبيت الشامل")
    print("=" * 50)
    print(f"التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # التحقق من Python
    check_python_version()
    
    # التحقق من pip
    if not check_pip():
        print("❌ لا يمكن المتابعة بدون pip")
        return
    
    # اختبار المكتبات
    print("=== اختبار المكتبات ===")
    
    installed_count = 0
    failed_packages = []
    
    for package_name, display_name in REQUIRED_PACKAGES:
        success, info = test_package_import(package_name, display_name)
        
        if success:
            print(f"✅ {display_name:<20} - الإصدار: {info}")
            installed_count += 1
        else:
            print(f"❌ {display_name:<20} - غير مثبت")
            failed_packages.append((package_name, display_name))
    
    print()
    print("=" * 50)
    print("📊 ملخص النتائج:")
    print(f"✅ المكتبات المثبتة: {installed_count}/{len(REQUIRED_PACKAGES)}")
    print(f"❌ المكتبات المفقودة: {len(failed_packages)}")
    
    if failed_packages:
        print("\n📋 المكتبات المفقودة:")
        for package_name, display_name in failed_packages:
            print(f"   - {display_name} ({package_name})")
        
        print("\n💡 لتثبيت المكتبات المفقودة:")
        print("pip install -r requirements.txt")
        print("\nأو تثبيت مكتبة واحدة:")
        for package_name, display_name in failed_packages[:5]:  # أول 5 فقط
            pip_name = package_name.replace('.', '-') if '.' in package_name else package_name
            print(f"pip install {pip_name}")
    else:
        print("\n🎉 تم تثبيت جميع المكتبات بنجاح!")
    
    print("\n" + "=" * 50)

def test_database_connections():
    """اختبار الاتصال بقواعد البيانات"""
    print("\n=== اختبار قواعد البيانات ===")
    
    # اختبار SQLite
    try:
        import sqlite3
        conn = sqlite3.connect(':memory:')
        conn.close()
        print("✅ SQLite متاح ويعمل")
    except Exception as e:
        print(f"❌ SQLite: {e}")
    
    # اختبار SQLAlchemy
    try:
        from sqlalchemy import create_engine
        engine = create_engine('sqlite:///:memory:')
        print("✅ SQLAlchemy متاح ويعمل")
    except Exception as e:
        print(f"❌ SQLAlchemy: {e}")
    
    # اختبار Redis (اختياري)
    try:
        import redis
        # لا نحاول الاتصال الفعلي لأن الخادم قد لا يكون يعمل
        print("✅ مكتبة Redis متاحة")
    except Exception as e:
        print(f"❌ Redis: {e}")
    
    # اختبار MongoDB (اختياري)
    try:
        import pymongo
        print("✅ مكتبة PyMongo متاحة")
    except Exception as e:
        print(f"❌ PyMongo: {e}")

def test_web_frameworks():
    """اختبار إطارات العمل"""
    print("\n=== اختبار إطارات العمل ===")
    
    # اختبار Flask
    try:
        from flask import Flask
        app = Flask(__name__)
        print("✅ Flask متاح ويعمل")
    except Exception as e:
        print(f"❌ Flask: {e}")
    
    # اختبار Django
    try:
        import django
        print(f"✅ Django متاح - الإصدار: {django.get_version()}")
    except Exception as e:
        print(f"❌ Django: {e}")
    
    # اختبار FastAPI
    try:
        from fastapi import FastAPI
        app = FastAPI()
        print("✅ FastAPI متاح ويعمل")
    except Exception as e:
        print(f"❌ FastAPI: {e}")

def generate_report():
    """إنشاء تقرير مفصل"""
    report_file = f"installation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("تقرير اختبار التثبيت\n")
        f.write("=" * 30 + "\n")
        f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"إصدار Python: {sys.version}\n")
        f.write(f"المنصة: {sys.platform}\n\n")
        
        # إضافة تفاصيل المكتبات
        f.write("حالة المكتبات:\n")
        f.write("-" * 20 + "\n")
        
        for package_name, display_name in REQUIRED_PACKAGES:
            success, info = test_package_import(package_name, display_name)
            status = "مثبت" if success else "غير مثبت"
            f.write(f"{display_name}: {status}\n")
    
    print(f"\n📄 تم إنشاء التقرير: {report_file}")

if __name__ == "__main__":
    try:
        run_installation_test()
        test_database_connections()
        test_web_frameworks()
        generate_report()
        
        print("\n🏁 انتهى الاختبار")
        print("💡 إذا كانت هناك مكتبات مفقودة، شغل:")
        print("   setup_environment.bat  (Windows)")
        print("   ./setup_environment.sh (Linux/Mac)")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("يرجى التحقق من تثبيت Python وإعادة المحاولة")
